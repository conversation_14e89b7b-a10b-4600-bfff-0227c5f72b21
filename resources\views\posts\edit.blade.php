<x-layouts.unilink-layout>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('posts.show', $post) }}" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Post</h1>
                <p class="text-gray-600 mt-1">Update your post content</p>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-3xl">
        <form action="{{ route('posts.update', $post) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf
            @method('PUT')
            
            <!-- User Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <img class="h-12 w-12 rounded-full" src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : '/default-avatar.svg' }}" alt="{{ auth()->user()->name }}" onerror="this.src='/default-avatar.svg'; this.onerror=null;">
                    <div>
                        <p class="font-medium text-gray-900">{{ auth()->user()->name }}</p>
                        <select name="organization_id" class="text-sm text-gray-600 border-0 bg-transparent focus:ring-0 p-0">
                            <option value="">Personal Post</option>
                            @foreach($organizations as $org)
                                <option value="{{ $org->id }}" {{ $post->organization_id == $org->id ? 'selected' : '' }}>{{ $org->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Tags -->
                @if($post->postMethod && $post->postMethod->activeTags->count() > 0)
                    <div class="mb-6" id="edit_tags_section">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tags ({{ $post->postMethod->name }})</label>
                        <div id="edit_tags_container" class="space-y-2">
                            @foreach($post->postMethod->activeTags as $tag)
                                <div class="flex items-center">
                                    <input type="checkbox" name="tags[]" value="{{ $tag->id }}" id="edit_tag_{{ $tag->id }}"
                                           class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded"
                                           {{ $post->tags->contains($tag->id) ? 'checked' : '' }}>
                                    <label for="edit_tag_{{ $tag->id }}" class="ml-2 text-sm text-gray-700 flex items-center">
                                        <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }}"></span>
                                        {{ $tag->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        @error('tags')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                @endif

                 <!-- Visibility -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Who can see this?</label>
                    <select name="visibility" id="edit_visibility_select" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" onchange="updateEditVisibilityDescription()">
                        @if($post->group_id)
                            <!-- Group post options -->
                            <option value="group_members_only" {{ old('visibility', $post->visibility) == 'group_members_only' ? 'selected' : '' }}>👥 Group Members Only</option>
                            <option value="public" {{ old('visibility', $post->visibility) == 'public' ? 'selected' : '' }}>🌐 Public</option>
                            <option value="same_school_only" {{ old('visibility', $post->visibility) == 'same_school_only' ? 'selected' : '' }}>🏫 Same school only</option>
                            <option value="same_campus_only" {{ old('visibility', $post->visibility) == 'same_campus_only' ? 'selected' : '' }}>🏛️ Same campus only</option>
                        @elseif($post->organization_id)
                            <!-- Organization post options -->
                            <option value="same_school_only" {{ old('visibility', $post->visibility) == 'same_school_only' ? 'selected' : '' }}>🏫 Same school only</option>
                            <option value="public" {{ old('visibility', $post->visibility) == 'public' ? 'selected' : '' }}>🌐 Public</option>
                            <option value="same_campus_only" {{ old('visibility', $post->visibility) == 'same_campus_only' ? 'selected' : '' }}>🏛️ Same campus only</option>
                            <option value="organization_only" {{ old('visibility', $post->visibility) == 'organization_only' ? 'selected' : '' }}>🏢 My Organization Only / Members</option>
                            <option value="officers_only" {{ old('visibility', $post->visibility) == 'officers_only' ? 'selected' : '' }}>🔒 Officer Only</option>
                        @else
                            <!-- Personal post options -->
                            <option value="followers_only" {{ old('visibility', $post->visibility) == 'followers_only' ? 'selected' : '' }}>🤝 Followers Only</option>
                            <option value="public" {{ old('visibility', $post->visibility) == 'public' ? 'selected' : '' }}>🌐 Public</option>
                            <option value="same_school_only" {{ old('visibility', $post->visibility) == 'same_school_only' ? 'selected' : '' }}>🏫 Same school only</option>
                            <option value="same_campus_only" {{ old('visibility', $post->visibility) == 'same_campus_only' ? 'selected' : '' }}>🏛️ Same campus only</option>
                            <option value="private" {{ old('visibility', $post->visibility) == 'private' ? 'selected' : '' }}>👤 Only Me / Private</option>
                        @endif
                    </select>
                    <p class="text-xs text-gray-500 mt-1" id="edit_visibility_description">
                        @switch(old('visibility', $post->visibility))
                            @case('public')
                                Anyone on or off UniLink can see this post
                                @break
                            @case('followers_only')
                                Only people who follow you can see this post
                                @break
                            @case('same_school_only')
                                Only people from your school can see this post
                                @break
                            @case('same_campus_only')
                                Only people from your campus can see this post
                                @break
                            @case('private')
                                Only you can see this post
                                @break
                            @case('organization_only')
                                Only members of your organization can see this post
                                @break
                            @case('officers_only')
                                Only officers of your organization can see this post
                                @break
                            @case('group_members_only')
                                Only members of this group can see this post
                                @break
                            @default
                                Anyone on or off UniLink can see this post
                        @endswitch
                    </p>
                    @error('visibility')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Title -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" name="title" value="{{ old('title', $post->title) }}" placeholder="What's the title of your post?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green text-lg" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Content -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                    <textarea name="content" rows="6" placeholder="What's on your mind?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required>{{ old('content', $post->content) }}</textarea>
                    @error('content')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Existing Images -->
                @if($post->images && count($post->images) > 0)
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Images</label>
                        <div class="grid grid-cols-2 gap-2">
                            @foreach($post->images as $index => $image)
                                <div class="relative">
                                    <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}"
                                         class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                         onclick="openImageModal({{ json_encode($post->images) }}, {{ $index }})">
                                    <label class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 cursor-pointer z-10">
                                        <input type="checkbox" name="remove_images[]" value="{{ $image }}" class="hidden" onchange="toggleImageRemoval(this)">
                                        ×
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Click images to view full size, or click × to mark for removal</p>
                    </div>
                @endif

                <!-- New Image Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Add New Images (Optional)</label>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center space-x-2 cursor-pointer text-custom-green hover:text-custom-second-darkest">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-sm font-medium">Add Photos</span>
                            <input type="file" name="images[]" multiple accept="image/*" class="hidden" onchange="previewImages(this)">
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">You can upload up to 5 images. Max 2MB each.</p>
                    
                    <!-- New Image Preview -->
                    <div id="image-preview" class="mt-3 grid grid-cols-2 gap-2 hidden"></div>
                    
                    @error('images')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @error('images.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Existing Attachments -->
                @if($post->fileAttachments && $post->fileAttachments->count() > 0)
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current File Attachments</label>
                        <div class="space-y-2">
                            @foreach($post->fileAttachments as $attachment)
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors attachment-item" data-attachment-id="{{ $attachment->id }}">
                                    <div class="flex-shrink-0">
                                        <i class="{{ app('App\Services\FileUploadService')->getFileTypeIcon($attachment->file_type) }} text-gray-500"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">{{ $attachment->original_filename }}</p>
                                        <p class="text-xs text-gray-500">{{ $attachment->formatted_size }}</p>
                                    </div>
                                    <div class="flex-shrink-0 flex items-center space-x-2">
                                        <a href="{{ $attachment->url }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                            Download
                                        </a>
                                        <label class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 cursor-pointer">
                                            <input type="checkbox" name="remove_attachments[]" value="{{ $attachment->id }}" class="hidden" onchange="toggleAttachmentRemoval(this)">
                                            ×
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Click × to mark attachments for removal</p>
                    </div>
                @endif

                <!-- New Attachment Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Add New File Attachments (Optional)</label>
                    @if($post->group_id)
                        @php
                            $group = $post->group;
                        @endphp
                        @if($group && $group->allow_file_sharing)
                            <!-- Group post attachments with group-specific settings -->
                            <x-file-upload-zone
                                name="attachments"
                                accept="{{ $group->allowed_file_types ? '.' . implode(',.', $group->allowed_file_types) : '*' }}"
                                :multiple="true"
                                max-size="{{ $group->max_file_size_mb }}MB"
                                :allowed-types="$group->allowed_file_types"
                                title="Upload Files"
                                description="Drag and drop files here or click to browse" />
                        @else
                            <!-- Group doesn't allow file sharing -->
                            <div class="text-sm text-gray-500 italic">
                                File attachments are not allowed in this group.
                            </div>
                        @endif
                    @else
                        <!-- Normal post attachments with default settings -->
                        <x-file-upload-zone
                            name="attachments"
                            accept=".pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.zip,.rar,.7z"
                            :multiple="true"
                            max-size="10MB"
                            :allowed-types="['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z']"
                            title="Upload Files"
                            description="Drag and drop files here or click to browse (PDF, DOC, images, videos, archives - max 10MB)" />
                    @endif
                    @error('attachments')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @error('attachments.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Facebook Embed URL -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Facebook Embed URL (Optional)</label>
                    <input type="url" name="facebook_embed_url" value="{{ old('facebook_embed_url', $post->facebook_embed_url) }}" placeholder="https://www.facebook.com/..." class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                    @error('facebook_embed_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Post Options -->
                <div class="mb-6">
                    <div class="flex items-center space-x-6">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_pinned" value="1" {{ $post->is_pinned ? 'checked' : '' }} class="rounded border-gray-300 text-custom-green shadow-sm focus:border-custom-green focus:ring focus:ring-custom-green focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Pin this post</span>
                        </label>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required>
                        <option value="published" {{ $post->status == 'published' ? 'selected' : '' }}>Published</option>
                        <option value="draft" {{ $post->status == 'draft' ? 'selected' : '' }}>Draft</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('dashboard') }}" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Update Post
                </button>
            </div>
        </form>
    </div>

    <script>
    function toggleImageRemoval(checkbox) {
        const container = checkbox.closest('.relative');
        const img = container.querySelector('img');

        if (checkbox.checked) {
            img.style.opacity = '0.5';
            container.style.border = '2px solid #ef4444';
        } else {
            img.style.opacity = '1';
            container.style.border = 'none';
        }
    }

    function toggleAttachmentRemoval(checkbox) {
        const container = checkbox.closest('.attachment-item');

        if (checkbox.checked) {
            container.style.opacity = '0.5';
            container.style.border = '2px solid #ef4444';
            container.style.backgroundColor = '#fee2e2';
        } else {
            container.style.opacity = '1';
            container.style.border = 'none';
            container.style.backgroundColor = '#f9fafb';
        }
    }

    function previewImages(input) {
        const preview = document.getElementById('image-preview');
        preview.innerHTML = '';
        
        if (input.files && input.files.length > 0) {
            preview.classList.remove('hidden');
            
            Array.from(input.files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'relative';
                        div.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg">
                            <button type="button" onclick="removeNewImage(${index}, this)" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                ×
                            </button>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            preview.classList.add('hidden');
        }
    }

    function removeNewImage(index, button) {
        const input = document.querySelector('input[name="images[]"]');
        const dt = new DataTransfer();
        
        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        input.files = dt.files;
        button.parentElement.remove();
        
        if (input.files.length === 0) {
            document.getElementById('image-preview').classList.add('hidden');
        }
    }

    // Update visibility description based on selection
    function updateEditVisibilityDescription() {
        const visibilitySelect = document.getElementById('edit_visibility_select');
        const descriptionElement = document.getElementById('edit_visibility_description');

        const descriptions = {
            'public': 'Anyone on or off UniLink can see this post',
            'followers_only': 'Only people who follow you can see this post',
            'same_school_only': 'Only people from your school can see this post',
            'same_campus_only': 'Only people from your campus can see this post',
            'private': 'Only you can see this post',
            'organization_only': 'Only members of your organization can see this post',
            'officers_only': 'Only officers of your organization can see this post',
            'group_members_only': 'Only members of this group can see this post'
        };

        descriptionElement.textContent = descriptions[visibilitySelect.value] || 'Anyone on or off UniLink can see this post';
    }

    // Tags are now loaded automatically based on context, no JavaScript needed
    </script>
</x-layouts.unilink-layout>
