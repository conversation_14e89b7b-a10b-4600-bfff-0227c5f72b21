@props(['comment', 'post'])

<div class="comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150" data-comment-id="{{ $comment->id }}">
    <div class="flex space-x-3">
        <a href="{{ route('profile.user', $comment->user) }}" class="flex-shrink-0">
            <img class="h-10 w-10 rounded-full ring-1 ring-gray-200 shadow-sm"
                 src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : '/default-avatar.svg' }}"
                 alt="{{ $comment->user->name }}"
                 onerror="this.src='/default-avatar.svg'; this.onerror=null;">
        </a>
        <div class="flex-1 min-w-0">
            <div class="bg-gray-50/70 rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-2 mb-2">
                    <a href="{{ route('profile.user', $comment->user) }}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                        {{ $comment->user->name }}
                    </a>
                    <span class="text-xs text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                    @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                        <div class="relative ml-auto" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                <button onclick="editComment({{ $comment->id }})"
                                        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    <span>Edit</span>
                                </button>
                                <button onclick="deleteComment({{ $comment->id }})"
                                        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>Delete</span>
                                </button>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="comment-content">
                    <p class="text-gray-700 text-sm leading-relaxed">{!! nl2br(e($comment->content)) !!}</p>
                </div>

                <!-- Edit form (hidden by default) -->
                <div class="comment-edit-form hidden mt-3">
                    <form class="edit-comment-form" data-comment-id="{{ $comment->id }}">
                        @csrf
                        @method('PUT')
                        <textarea name="content" rows="2"
                                  class="w-full border border-gray-200 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">{{ $comment->content }}</textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button type="button" onclick="cancelEditComment({{ $comment->id }})"
                                    class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                            <button type="submit"
                                    class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Comment Actions -->
            <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                <!-- Facebook-style Reactions -->
                <x-facebook-reactions :target="$comment" target-type="comment" :show-count="true" />

                @auth
                    <button onclick="showReplyForm({{ $comment->id }})" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                        </svg>
                        <span class="font-medium">Reply</span>
                    </button>
                @endauth

                @if($comment->created_at != $comment->updated_at)
                    <span class="text-xs text-gray-400 italic">• edited</span>
                @endif
            </div>

            <!-- Reply Form (hidden by default) -->
            <div class="reply-form hidden mt-3 ml-4" id="reply-form-{{ $comment->id }}">
                @auth
                    <form class="comment-form" data-post-id="{{ $post->id }}" data-parent-id="{{ $comment->id }}">
                        @csrf
                        <div class="flex space-x-3">
                            <div class="flex-shrink-0">
                                <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                     src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : '/default-avatar.svg' }}"
                                     alt="{{ auth()->user()->name }}"
                                     onerror="this.src='/default-avatar.svg'; this.onerror=null;">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="relative">
                                    <textarea name="content" rows="1"
                                              placeholder="Write a reply..."
                                              class="w-full px-3 py-2 border border-gray-200 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200"
                                              required></textarea>
                                </div>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="hideReplyForm({{ $comment->id }})"
                                            class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                        Reply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                @endauth
            </div>

            <!-- Replies -->
            @if($comment->replies->count() > 0)
                <div class="nested-comments mt-4 ml-4 space-y-3 border-l-2 border-gray-100 pl-4">
                    @foreach($comment->replies as $reply)
                        <x-comment-item :comment="$reply" :post="$post" />
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
