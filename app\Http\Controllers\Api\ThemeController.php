<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ThemeController extends Controller
{
    /**
     * Get the current user's theme preference
     */
    public function getThemePreference(): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return response()->json([
            'theme' => $user->theme_preference ?? 'light'
        ]);
    }

    /**
     * Update the current user's theme preference
     */
    public function updateThemePreference(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $validated = $request->validate([
            'theme' => ['required', 'string', Rule::in(['light', 'dark'])]
        ]);

        $user->update([
            'theme_preference' => $validated['theme']
        ]);

        return response()->json([
            'success' => true,
            'theme' => $validated['theme']
        ]);
    }
}
