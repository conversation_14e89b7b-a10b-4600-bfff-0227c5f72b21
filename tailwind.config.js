import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    darkMode: 'class', // Enable class-based dark mode

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                'custom': {
                    'lightest': '#EEEEEE',      // Light Gray
                    'green': '#7BC74D',         // Green
                    'dark-gray': '#393E46',     // Dark Gray
                    'darkest': '#222831',       // Dark Navy
                    // Dark mode variants
                    'dark': {
                        'bg-primary': '#1a1a1a',     // Main dark background
                        'bg-secondary': '#2d2d2d',   // Secondary dark background
                        'bg-tertiary': '#3a3a3a',    // Tertiary dark background
                        'text-primary': '#ffffff',   // Primary dark text
                        'text-secondary': '#d1d5db', // Secondary dark text
                        'text-muted': '#9ca3af',     // Muted dark text
                        'border': '#404040',         // Dark border color
                        'hover': '#4a4a4a',         // Dark hover state
                    }
                }
            },
        },
    },

    plugins: [forms],
};
