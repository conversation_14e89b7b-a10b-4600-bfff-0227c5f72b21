@props(['user'])

<!-- Personal Post Creation Modal -->
<div id="personal-post-modal" class="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 hidden items-center justify-center z-50 theme-transition">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto theme-transition">
        <!-- Modal Header -->
        <div class="p-6 border-b border-gray-200 dark:border-gray-700 theme-transition">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 theme-transition">
                    Create Personal Post
                </h3>
                <button onclick="closePersonalPostModal()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 theme-transition">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form id="personal-post-form" action="{{ route('posts.store') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf

            <!-- User Info -->
            <div class="flex items-center space-x-3">
                <img class="h-10 w-10 rounded-full"
                     src="{{ auth()->user()->getAvatarUrl(40) }}"
                     alt="{{ auth()->user()->name }}">
                <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100 theme-transition">{{ auth()->user()->name }}</p>
                    <select name="organization_id" id="personal_organization_select" class="text-xs text-gray-500 dark:text-gray-400 border-0 bg-transparent dark:bg-transparent focus:ring-0 pt-0 pb-0 pl-0 theme-transition" onchange="loadPersonalContextTags()">
                        <option value="">Personal</option>
                        @foreach(auth()->user()->activeOrganizations()->get() as $org)
                            <option value="{{ $org->id }}">{{ $org->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Error Messages -->
            <div id="personal-error-messages" class="hidden p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg"></div>

            <!-- Visibility -->
            <div>
                <label for="personal_visibility_select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 theme-transition">Who can see this?</label>
                <select name="visibility" id="personal_visibility_select"
                        class="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 theme-transition"
                        onchange="updatePersonalVisibilityIcon()">
                    <option value="followers_only" selected>🤝 Followers Only</option>
                    <option value="public">🌐 Public</option>
                    <option value="same_school_only">🏫 Same school only</option>
                    <option value="same_campus_only">🏛️ Same campus only</option>
                    <option value="private">👤 Only Me / Private</option>
                </select>
                <p class="text-xs text-gray-500 mt-1" id="personal_visibility_description">Only people who follow you can see this post</p>
            </div>

            <!-- Tags -->
            <div id="personal_tags_section">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 theme-transition">Post Tags (Select multiple)</label>
                <div id="personal_tags_container" class="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                    <!-- Tags will be loaded dynamically based on context -->
                </div>
                <p class="mt-2 text-xs text-gray-500 dark:text-gray-400 theme-transition">Select one or more tags that best describe your post</p>
            </div>

            <!-- Title -->
            <div>
                <label for="personal_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 theme-transition">Title</label>
                <input type="text" name="title" id="personal_title"
                       class="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 theme-transition"
                       placeholder="What's your post about?" required>
            </div>

            <!-- Content -->
            <div>
                <label for="personal_content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 theme-transition">Content</label>
                <textarea name="content" id="personal_content" rows="4"
                          class="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 theme-transition"
                          placeholder="What's on your mind?" required></textarea>
            </div>

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Images</label>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md hover:border-gray-400 dark:hover:border-gray-500 transition-colors theme-transition">
                    <div class="space-y-1 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="flex text-sm text-gray-600 dark:text-gray-400">
                            <label for="personal_images" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                <span>Upload images</span>
                                <input id="personal_images" name="images[]" type="file" multiple accept="image/*" class="sr-only" onchange="previewPersonalImages(this)">
                            </label>
                            <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB each</p>
                    </div>
                </div>
                <div id="personal-image-preview" class="mt-4 hidden grid grid-cols-2 gap-4"></div>
            </div>

            <!-- File Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 theme-transition">File Attachments</label>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md hover:border-gray-400 dark:hover:border-gray-500 transition-colors theme-transition">
                    <div class="space-y-1 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                        <div class="flex text-sm text-gray-600 dark:text-gray-400">
                            <label for="personal_attachments" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                <span>Upload files</span>
                                <input id="personal_attachments" name="attachments[]" type="file" multiple class="sr-only" onchange="previewPersonalAttachments(this)">
                            </label>
                            <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 theme-transition">Any file type up to 50MB each</p>
                    </div>
                </div>
                <div id="personal-attachment-preview" class="mt-4 hidden space-y-2"></div>
            </div>

            <!-- Facebook Embed URL -->
            <div>
                <label for="personal_facebook_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 theme-transition">Facebook Post URL (Optional)</label>
                <input type="url" name="facebook_embed_url" id="personal_facebook_url"
                       class="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 theme-transition"
                       placeholder="https://www.facebook.com/...">
            </div>

            <!-- Status -->
            <input type="hidden" name="status" value="published">

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700 theme-transition">
                <button type="button" onclick="closePersonalPostModal()"
                        class="bg-gray-500 dark:bg-gray-600 text-white px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500 theme-transition">
                    Cancel
                </button>
                <button type="submit" id="personal-submit-btn"
                        class="bg-custom-green text-white px-6 py-2 rounded-md font-medium hover:bg-green-400 dark:hover:bg-green-500 theme-transition">
                    <span id="personal-submit-text">Post</span>
                    <span id="personal-loading-text" class="hidden">Creating...</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function previewPersonalImages(input) {
    const preview = document.getElementById('personal-image-preview');
    preview.innerHTML = '';

    if (input.files && input.files.length > 0) {
        preview.classList.remove('hidden');
        Array.from(input.files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.className = 'relative';
                    div.innerHTML = `
                        <img src="${e.target.result}" class="w-full h-32 object-cover rounded-lg">
                        <button type="button" onclick="removePersonalImage(${index}, this)" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                            ×
                        </button>
                    `;
                    preview.appendChild(div);
                };
                reader.readAsDataURL(file);
            }
        });
    } else {
        preview.classList.add('hidden');
    }
}

function previewPersonalAttachments(input) {
    const preview = document.getElementById('personal-attachment-preview');
    preview.innerHTML = '';

    if (input.files && input.files.length > 0) {
        preview.classList.remove('hidden');

        Array.from(input.files).forEach((file, index) => {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 theme-transition';

            // Get file icon based on type
            let fileIcon = '📄'; // Default
            if (file.type.includes('pdf')) fileIcon = '📄';
            else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) fileIcon = '📝';
            else if (file.type.includes('sheet') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) fileIcon = '📊';
            else if (file.type.includes('presentation') || file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) fileIcon = '📊';

            fileDiv.innerHTML = `
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">${fileIcon}</span>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100 theme-transition">${file.name}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 theme-transition">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                </div>
                <button type="button" onclick="removePersonalAttachment(${index})" class="text-red-500 hover:text-red-700">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            `;
            preview.appendChild(fileDiv);
        });
    } else {
        preview.classList.add('hidden');
    }
}

function removePersonalImage(index, button) {
    const input = document.querySelector('input[name="images[]"]');
    const dt = new DataTransfer();
    
    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });
    
    input.files = dt.files;
    button.parentElement.remove();
    
    if (input.files.length === 0) {
        document.getElementById('personal-image-preview').classList.add('hidden');
    }
}

function removePersonalAttachment(index) {
    const input = document.querySelector('input[name="attachments[]"]');
    const dt = new DataTransfer();

    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    input.files = dt.files;

    // Remove the preview element
    const preview = document.getElementById('personal-attachment-preview');
    const attachments = preview.children;
    if (attachments[index]) {
        attachments[index].remove();
    }

    // Check if preview is empty and hide if so
    if (preview.children.length === 0) {
        preview.classList.add('hidden');
    }
}

// Handle form submission with AJAX
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('personal-post-form');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('personal-submit-btn');
            const submitText = document.getElementById('personal-submit-text');
            const loadingText = document.getElementById('personal-loading-text');
            const errorDiv = document.getElementById('personal-error-messages');

            // Show loading state
            submitBtn.disabled = true;
            submitText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            errorDiv.classList.add('hidden');

            try {
                const formData = new FormData(form);
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    // Success - close modal and refresh page or update feed
                    closePersonalPostModal();
                    window.location.reload(); // Simple refresh for now
                } else {
                    const errorData = await response.json();
                    let errorMessage = 'An error occurred while creating the post.';
                    
                    if (errorData.errors) {
                        errorMessage = Object.values(errorData.errors).flat().join('<br>');
                    } else if (errorData.message) {
                        errorMessage = errorData.message;
                    }
                    
                    errorDiv.innerHTML = errorMessage;
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.innerHTML = '<p class="text-sm text-red-600">Network error. Please try again.</p>';
                errorDiv.classList.remove('hidden');
    } finally {
        const submitBtn = document.getElementById('personal-submit-btn');
        const submitText = document.getElementById('personal-submit-text');
        const loadingText = document.getElementById('personal-loading-text');

        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    }
        });
    }
});

// Simple modal functions
function openPersonalPostModal() {
    document.getElementById('personal-post-modal').classList.remove('hidden');
    document.getElementById('personal-post-modal').classList.add('flex');
    document.body.style.overflow = 'hidden';
    // Load tags for default context (personal post)
    loadPersonalContextTags();
}

function closePersonalPostModal() {
    document.getElementById('personal-post-modal').classList.add('hidden');
    document.getElementById('personal-post-modal').classList.remove('flex');
    document.body.style.overflow = 'auto';

    // Reset form
    document.querySelector('#personal-post-modal form').reset();

    // Reset image preview
    const imagePreview = document.getElementById('personal-image-preview');
    imagePreview.classList.add('hidden');
    imagePreview.innerHTML = '';

    // Reset attachment preview
    const attachmentPreview = document.getElementById('personal-attachment-preview');
    attachmentPreview.classList.add('hidden');
    attachmentPreview.innerHTML = '';

    // Reset error messages
    document.getElementById('personal-error-messages').classList.add('hidden');

    // Reset checkboxes
    const checkboxes = document.querySelectorAll('#personal_tags_container input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    // Reset visibility to default
    document.getElementById('personal_visibility_select').value = 'followers_only';
    updatePersonalVisibilityIcon();
}

// Update visibility description based on selection
function updatePersonalVisibilityIcon() {
    const visibilitySelect = document.getElementById('personal_visibility_select');
    const descriptionElement = document.getElementById('personal_visibility_description');

    const descriptions = {
        'followers_only': 'Only people who follow you can see this post',
        'public': 'Anyone on or off UniLink can see this post',
        'same_school_only': 'Only people from your school can see this post',
        'same_campus_only': 'Only people from your campus can see this post',
        'private': 'Only you can see this post'
    };

    descriptionElement.textContent = descriptions[visibilitySelect.value] || '';
}

// Load tags based on context (organization selection)
async function loadPersonalContextTags() {
    const organizationSelect = document.getElementById('personal_organization_select');
    const tagsContainer = document.getElementById('personal_tags_container');
    
    const organizationId = organizationSelect.value;
    
    // Determine post method based on context
    let postMethodSlug = 'user'; // Default for personal posts
    if (organizationId) {
        postMethodSlug = 'organization';
    }

    try {
        // Get the post method
        const postMethods = @json(\App\Models\PostMethod::with('activeTags')->active()->get());
        const postMethod = postMethods.find(method => method.slug === postMethodSlug);
        
        if (postMethod && postMethod.active_tags && postMethod.active_tags.length > 0) {
            tagsContainer.innerHTML = '';
            
            // Create checkboxes for each tag
            postMethod.active_tags.forEach(tag => {
                const tagDiv = document.createElement('label');
                tagDiv.className = 'flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors theme-transition';
                tagDiv.innerHTML = `
                    <input type="checkbox" name="tags[]" value="${tag.id}"
                           class="h-4 w-4 text-blue-600 dark:text-blue-500 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <div class="ml-3 flex items-center">
                        <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: ${tag.color}"></span>
                        <span class="text-sm text-gray-700 dark:text-gray-300 theme-transition">${tag.name}</span>
                    </div>
                `;
                tagsContainer.appendChild(tagDiv);
            });
        } else {
            tagsContainer.innerHTML = '<p class="text-sm text-gray-500 dark:text-gray-400 theme-transition">No tags available for this context.</p>';
        }
    } catch (error) {
        console.error('Error loading tags:', error);
        tagsContainer.innerHTML = '<p class="text-sm text-red-500">Error loading tags.</p>';
    }
}
</script>
