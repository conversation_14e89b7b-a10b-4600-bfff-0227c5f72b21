@props([
    'name' => '',
    'class' => 'w-5 h-5',
    'fill' => 'currentColor',
    'stroke' => 'none'
])

@php
    $iconPath = public_path("assets/SVG_Icons/{$name}.svg");
    $iconExists = file_exists($iconPath);

    if ($iconExists) {
        $iconContent = file_get_contents($iconPath);
        $iconContent = preg_replace('/<style[^>]*>.*?<\/style>/s', '', $iconContent);
        // Remove XML declaration and comments for cleaner output
        $iconContent = preg_replace('/<\?xml[^>]*\?>/', '', $iconContent);
        $iconContent = preg_replace('/<!--.*?-->/s', '', $iconContent);
        $iconContent = trim($iconContent);

        // Extract viewBox from the original SVG
        preg_match('/viewBox="([^"]*)"/', $iconContent, $viewBoxMatches);
        $viewBox = $viewBoxMatches[1] ?? '0 0 24 24';

        // Extract the inner content (paths, circles, etc.)
        preg_match('/<svg[^>]*>(.*?)<\/svg>/s', $iconContent, $contentMatches);
        $innerContent = $contentMatches[1] ?? '';

        // Clean up the inner content and replace hardcoded colors with currentColor if needed
        $innerContent = trim($innerContent);
        // Replace hardcoded black fills with currentColor for better theming
        $innerContent = str_replace('fill="#000000"', 'fill="currentColor"', $innerContent);
        $innerContent = str_replace('fill="#000"', 'fill="currentColor"', $innerContent);
        $innerContent = str_replace('stroke="#000000"', 'stroke="currentColor"', $innerContent);
        $innerContent = str_replace('stroke="#000"', 'stroke="currentColor"', $innerContent);
    }
@endphp

@if($iconExists)
    <svg {{ $attributes->merge(['class' => $class]) }} 
         viewBox="{{ $viewBox }}" 
         fill="{{ $fill }}" 
         stroke="{{ $stroke }}"
         xmlns="http://www.w3.org/2000/svg">
        {!! $innerContent !!}
    </svg>
@else
    <!-- Fallback icon if SVG not found -->
    <svg {{ $attributes->merge(['class' => $class]) }} 
         fill="none" 
         stroke="currentColor" 
         viewBox="0 0 24 24" 
         xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
@endif
