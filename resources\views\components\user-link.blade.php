@props(['user', 'size' => 'sm', 'showAvatar' => true])

@php
    $sizeClasses = [
        'xs' => 'h-6 w-6',
        'sm' => 'h-8 w-8', 
        'md' => 'h-10 w-10',
        'lg' => 'h-12 w-12',
        'xl' => 'h-16 w-16'
    ];
    
    $textSizeClasses = [
        'xs' => 'text-xs',
        'sm' => 'text-sm',
        'md' => 'text-base',
        'lg' => 'text-lg',
        'xl' => 'text-xl'
    ];
    
    $avatarSize = $sizeClasses[$size] ?? $sizeClasses['sm'];
    $textSize = $textSizeClasses[$size] ?? $textSizeClasses['sm'];
@endphp

<a href="{{ route('profile.user', $user) }}" class="flex items-center space-x-2 hover:opacity-80 transition-opacity" {{ $attributes }}>
    @if($showAvatar)
        <img class="{{ $avatarSize }} rounded-full flex-shrink-0"
             src="{{ $user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($user->avatar) : '/default-avatar.svg' }}"
             alt="{{ $user->name }}"
             onerror="this.src='/default-avatar.svg'; this.onerror=null;">
    @endif
    <span class="{{ $textSize }} font-medium text-gray-900 hover:text-custom-green">{{ $user->name }}</span>
</a>
