<!-- <PERSON><PERSON> -->
<div class="px-4 py-4 bg-custom-green dark:bg-custom-green-600">
    <h2 class="text-lg font-bold text-custom-darkest dark:text-white px-3 flex items-center">
        <svg class="w-5 h-5 mr-3 dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
        Menu
    </h2>
</div>

<!-- Navigation Menu -->
<nav class="flex-1 px-4 py-4 space-y-2 theme-transition dark:bg-gray-800">
    <!-- Profile -->
    <a href="{{ route('profile.show') }}" class="flex items-center px-3 py-2 text-medium font-medium rounded-lg theme-transition {{ request()->routeIs('profile*') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
        <x-svg-icon name="Profile" class="w-6 h-6 mr-3 {{ request()->routeIs('profile*') ? 'text-white' : 'dark:text-gray-200' }}" />
        Profile
    </a>

    <!-- University -->
    <div class="space-y-1" x-data="{
        open: localStorage.getItem('universityDropdownOpen') === 'true',
        toggle() {
            this.open = !this.open;
            localStorage.setItem('universityDropdownOpen', this.open);
        }
    }">
        <button type="button" @click="toggle()" class="flex items-center w-full px-3 py-2 text-medium font-medium focus:outline-none focus:bg-custom-lightest dark:focus:bg-gray-700 rounded-lg {{ request()->routeIs('university*') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
            <x-svg-icon name="University" class="w-6 h-6 mr-3 {{ request()->routeIs('university*') ? 'text-white' : 'dark:text-gray-200' }}" />
            <span class="flex-1 text-left">University</span>
            <svg :class="{'transform rotate-90': open}" class="w-4 h-4 ml-auto transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
        <div x-show="open" x-transition class="space-y-1 pl-2">
            <a href="{{ route('university.index') }}" class="flex items-center px-6 py-2 text-sm rounded-lg {{ request()->routeIs('university.index') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
                <x-svg-icon name="University" class="w-4 h-4 mr-3 {{ request()->routeIs('university.index') ? 'text-white' : 'dark:text-gray-200' }}" />
                University Overview
            </a>
            <a href="{{ route('university.campuses') }}" class="flex items-center px-6 py-2 text-sm rounded-lg {{ request()->routeIs('university.campuses*') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}"> 
                <x-svg-icon name="Groups" class="w-4 h-4 mr-3" />
                Campuses
            </a>
            <a href="{{ route('university.offices') }}" class="flex items-center px-6 py-2 text-sm rounded-lg {{ request()->routeIs('university.offices') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
                <x-svg-icon name="Organizations" class="w-4 h-4 mr-3" />
                Offices & Directory
            </a>
        </div>
    </div>

    <!-- Organizations & Pages -->
    <a href="{{ route('organizations.index') }}" class="flex items-center px-3 py-2 text-medium font-medium rounded-lg {{ request()->routeIs('organizations*') || request()->routeIs('pages*') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
        <x-svg-icon name="Organizations" class="w-6 h-6 mr-3 {{ request()->routeIs('organizations*') || request()->routeIs('pages*') ? 'text-white' : 'dark:text-gray-200' }}" />
        Organizations
    </a>

    <!-- Student Groups -->
    <a href="{{ route('groups.index') }}" class="flex items-center px-3 py-2 text-medium font-medium rounded-lg {{ request()->routeIs('groups*') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
        <x-svg-icon name="Groups" class="w-6 h-6 mr-3 {{ request()->routeIs('groups*') ? 'text-white' : 'dark:text-gray-200' }}" />
        Groups
    </a>

    <!-- Follow Management -->
    <div class="space-y-1" x-data="{
        open: localStorage.getItem('connectionsDropdownOpen') === 'true',
        toggle() {
            this.open = !this.open;
            localStorage.setItem('connectionsDropdownOpen', this.open);
        }
    }">
        <button type="button" @click="toggle()" class="flex items-center w-full px-3 py-2 text-medium font-medium focus:outline-none focus:bg-custom-lightest dark:focus:bg-gray-700 rounded-lg {{ request()->routeIs('follow-management*') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
            <x-svg-icon name="Connections" class="w-6 h-6 mr-3 {{ request()->routeIs('follow-management*') ? 'text-white' : 'dark:text-gray-200' }}" />
            <span class="flex-1 text-left ">Connections</span>
            <svg :class="{'transform rotate-90': open}" class="w-4 h-4 ml-auto transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
        <div x-show="open" x-transition class="space-y-1 pl-2">
            <a href="{{ route('follow-management.followers') }}" class="flex items-center px-6 py-2 text-sm rounded-lg {{ request()->routeIs('follow-management.followers') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
                <x-svg-icon name="Followers" class="w-4 h-4 mr-3 {{ request()->routeIs('follow-management.followers') ? 'text-white' : 'dark:text-gray-200' }}" />
                My Followers
            </a>
            <a href="{{ route('follow-management.following') }}" class="flex items-center px-6 py-2 text-sm rounded-lg {{ request()->routeIs('follow-management.following') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
                <x-svg-icon name="Following" class="w-4 h-4 mr-3 {{ request()->routeIs('follow-management.following') ? 'text-white' : 'dark:text-gray-200' }}" />
                Following
            </a>
        </div>
    </div>

    <!-- Find Scholarships -->
    <a href="{{ route('scholarships.index') }}" class="flex items-center px-3 py-2 text-medium font-medium rounded-lg {{ request()->routeIs('scholarships*') ? 'bg-custom-green dark:bg-custom-green text-white' : 'text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green' }}">
        <x-svg-icon name="Scholarships" class="w-6 h-6 mr-3 {{ request()->routeIs('scholarships*') ? 'text-white' : 'dark:text-gray-200' }}" />
        Scholarships
    </a>

    <!-- Admin Dashboard -->
    @if(auth()->user()->hasManagementAccess())
        <a href="{{ route('admin.dashboard') }}" class="flex items-center px-3 py-2 text-medium font-medium rounded-lg text-custom-darkest dark:text-gray-200 hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green">
            <x-svg-icon name="Admin" class="w-6 h-6 mr-3 font-light dark:text-gray-200" />
            Admin Dashboard
        </a>
    @endif
</nav>

    <!-- Logout -->
    <form method="POST" action="{{ route('logout') }}" onsubmit="handleLogout(event)">
        @csrf
        <button type="submit" class="flex items-center w-full px-6 py-2 text-medium font-medium text-custom-darkest dark:text-gray-200 rounded-lg hover:bg-custom-second-darkest hover:text-custom-green dark:hover:bg-gray-700 dark:hover:text-custom-green">
            <x-svg-icon name="Lagout" class="w-6 h-6 mr-3 dark:text-gray-200" />
            Logout
        </button>
    </form>
</nav>



