<?php

namespace App\Http\Controllers;

use App\Models\Group;
use App\Models\Organization;
use App\Models\Post;
use App\Models\School;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class GroupController extends Controller
{
    /**
     * Display a listing of groups
     */
    public function index(Request $request)
    {
        $query = Group::with(['creator', 'organization', 'school', 'campus'])
            ->active()
            ->withCount('activeMembers as active_members_count');

        // Filter by visibility - now show both public and private groups
        if ($request->has('visibility') && in_array($request->visibility, ['public', 'private'])) {
            $query->where('visibility', $request->visibility);
        }
        // Remove the else clause that limited to public groups only

        // Filter by organization
        if ($request->has('organization_id') && $request->organization_id) {
            $query->where('organization_id', $request->organization_id);
        }

        // Search by name
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $groups = $query->orderBy('name')->paginate(12);

        $organizations = Organization::active()->orderBy('name')->get();

        return view('groups.index', compact('groups', 'organizations'));
    }

    /**
     * Search groups for dynamic search functionality
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (empty($query) || strlen($query) < 1) {
            return response()->json([
                'success' => true,
                'groups' => []
            ]);
        }

        // Search groups by name and description
        $groups = Group::with(['creator', 'organization'])
            ->where('status', 'active')
            ->where('visibility', 'public') // Only show public groups in search
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->withCount('activeMembers')
            ->orderBy('name')
            ->limit(10) // Limit results for performance
            ->get()
            ->map(function($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'description' => $group->description ? Str::limit($group->description, 100) : null,
                    'logo_url' => $group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($group->logo) : null,
                    'cover_image_url' => $group->cover_image ? \Illuminate\Support\Facades\Storage::disk('public')->url($group->cover_image) : null,
                    'active_members_count' => $group->active_members_count,
                    'creator_name' => $group->creator->name,
                    'organization_name' => $group->organization ? $group->organization->name : null,
                    'visibility' => $group->visibility,
                    'url' => route('groups.show', $group),
                    'user_membership' => auth()->check() ? $group->members()->where('user_id', auth()->id())->first() : null
                ];
            });

        return response()->json([
            'success' => true,
            'groups' => $groups
        ]);
    }

    /**
     * Display user's groups
     */
    public function my()
    {
        $user = auth()->user();

        $myGroups = $user->activeGroups()
            ->with(['creator', 'organization'])
            ->withCount('activeMembers as active_members_count')
            ->orderBy('name')
            ->get();

        $pendingGroups = $user->groups()
            ->with(['creator', 'organization'])
            ->withCount('activeMembers as active_members_count')
            ->wherePivot('status', 'pending')
            ->orderBy('name')
            ->get();

        return view('groups.my', compact('myGroups', 'pendingGroups'));
    }

    /**
     * Show the form for creating a new group
     */
    public function create()
    {
        $organizations = Organization::active()->orderBy('name')->get();
        $schools = School::active()->with('activeCampuses')->get();
        return view('groups.create', compact('organizations', 'schools'));
    }

    /**
     * Store a newly created group
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'organization_id' => 'nullable|exists:organizations,id',
            'school_id' => 'required|exists:schools,id',
            'campus_id' => 'required|exists:campuses,id',
            'visibility' => 'required|in:public,private',
            'post_approval' => 'required|in:none,required',
            'allow_file_sharing' => 'boolean',
            'allowed_file_types' => 'nullable|array',
            'max_file_size_mb' => 'integer|min:1|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $validated['slug'] = Group::generateSlug($validated['name']);
        $validated['created_by'] = auth()->id();
        $validated['status'] = 'active';

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('groups/logos', 'public');
        }

        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('groups/covers', 'public');
        }

        $group = Group::create($validated);

        // Add creator as admin
        $group->members()->attach(auth()->id(), [
            'role' => 'admin',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        return redirect()->route('groups.show', $group)
            ->with('success', 'Group created successfully!');
    }

    /**
     * Display the specified group
     */
    public function show(Group $group)
    {
        // For private groups, we'll show basic info even to non-members
        // but restrict content access later in the view
        $isPrivateGroupNonMember = false;

        if ($group->visibility === 'private') {
            if (!auth()->check()) {
                return redirect()->route('login')->with('error', 'Please login to view this private group.');
            }

            // Check if user is not a member and not an admin
            if (!$group->hasActiveMember(auth()->user()) && !auth()->user()->isAdmin()) {
                $isPrivateGroupNonMember = true;
            }
        }

        $group->load([
            'creator',
            'organization',
            'school',
            'campus'
        ]);

        // Load active members separately to avoid any casting conflicts
        $activeMembers = $group->activeMembers()
            ->orderBy('group_members.role', 'desc')
            ->orderBy('group_members.joined_at')
            ->get();

        // For private groups where user is not a member, only load basic info
        if ($isPrivateGroupNonMember) {
            // Only load basic member count for display
            $group->setRelation('activeMembers', $activeMembers);
            $group->setRelation('pendingMembers', collect());
            $group->setRelation('approvedPosts', collect());

            $userMembership = null;
            if (auth()->check()) {
                $userMembership = $group->members()
                    ->where('user_id', auth()->id())
                    ->first();
            }

            return view('groups.show', compact('group', 'userMembership', 'isPrivateGroupNonMember'));
        }

        // Load pending members for moderators
        $pendingMembers = collect();
        if (auth()->check() && $group->userCanModerate(auth()->user())) {
            $pendingMembers = $group->pendingMembers()->get();
        }

        // Explicitly set the relationships to ensure they're collections
        $group->setRelation('activeMembers', $activeMembers);
        $group->setRelation('pendingMembers', $pendingMembers);

        // Load approved posts separately to ensure proper filtering
        $approvedPosts = $group->posts()
            ->where('approval_status', 'approved')
            ->where('status', 'published')
            ->with(['user', 'group', 'likes', 'comments.user', 'comments.reactions', 'comments.replies.user', 'comments.replies.reactions', 'fileAttachments'])
            ->latest('published_at')
            ->limit(10)
            ->get();

        $group->setRelation('approvedPosts', $approvedPosts);

        $userMembership = null;
        if (auth()->check()) {
            $userMembership = $group->members()
                ->where('user_id', auth()->id())
                ->first();
        }

        return view('groups.show', compact('group', 'userMembership', 'isPrivateGroupNonMember'));
    }

    /**
     * Show the form for editing the group
     */
    public function edit(Group $group)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to edit this group.');
        }

        $organizations = Organization::active()->orderBy('name')->get();
        $schools = School::active()->with('activeCampuses')->get();
        return view('groups.edit', compact('group', 'organizations', 'schools'));
    }

    /**
     * Update the specified group
     */
    public function update(Request $request, Group $group)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to edit this group.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'organization_id' => 'nullable|exists:organizations,id',
            'school_id' => 'required|exists:schools,id',
            'campus_id' => 'required|exists:campuses,id',
            'visibility' => 'required|in:public,private',
            'post_approval' => 'required|in:none,required',
            'allow_file_sharing' => 'boolean',
            'allowed_file_types' => 'nullable|array',
            'max_file_size_mb' => 'integer|min:1|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $group->name) {
            $validated['slug'] = Group::generateSlug($validated['name']);
        }

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('groups/logos', 'public');
        }

        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('groups/covers', 'public');
        }

        $group->update($validated);

        return redirect()->route('groups.show', $group)
            ->with('success', 'Group updated successfully!');
    }

    /**
     * Remove the specified group
     */
    public function destroy(Group $group)
    {
        // Only creator or admin can delete
        if ($group->created_by !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'You do not have permission to delete this group.');
        }

        $group->delete();

        return redirect()->route('groups.index')
            ->with('success', 'Group deleted successfully!');
    }

    /**
     * Join a group
     */
    public function join(Group $group)
    {
        $user = auth()->user();

        // Check if already a member
        if ($group->hasMember($user)) {
            return back()->with('error', 'You are already a member of this group.');
        }

        // For private groups, join as pending; for public groups, join as active
        $status = $group->visibility === 'private' ? 'pending' : 'active';

        $group->members()->attach($user->id, [
            'role' => 'member',
            'status' => $status,
            'joined_at' => $status === 'active' ? now() : null,
        ]);

        // Send notifications
        if ($status === 'pending') {
            // Notify group admins/moderators about membership request
            $group->moderators()->each(function ($admin) use ($user, $group) {
                $admin->notify(new \App\Notifications\GroupMembershipRequest($user, $group));
            });
        } else {
            // Notify group creator about new member joining
            if ($group->creator && $group->creator->id !== $user->id) {
                $group->creator->notify(new \App\Notifications\GroupMemberJoined($user, $group));
            }
        }

        $message = $status === 'pending'
            ? 'Join request sent! Waiting for approval.'
            : 'Successfully joined the group!';

        return back()->with('success', $message);
    }

    /**
     * Leave a group
     */
    public function leave(Group $group)
    {
        $user = auth()->user();

        // Check if user is a member
        if (!$group->hasMember($user)) {
            return back()->with('error', 'You are not a member of this group.');
        }

        // Prevent group creator from leaving
        if ($group->created_by === $user->id) {
            return back()->with('error', 'Group creators cannot leave their own group.');
        }

        $group->members()->detach($user->id);

        return back()->with('success', 'You have left the group.');
    }

    /**
     * Manage group members
     */
    public function members(Group $group)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to manage members.');
        }

        // Load members explicitly to ensure proper collections
        $activeMembers = $group->activeMembers()->get();
        $pendingMembers = $group->pendingMembers()->get();

        $group->setRelation('activeMembers', $activeMembers);
        $group->setRelation('pendingMembers', $pendingMembers);

        return view('groups.members', compact('group'));
    }

    /**
     * Display all join requests for groups the user moderates
     */
    public function joinRequests()
    {
        $user = auth()->user();

        // Get all groups where the user is a moderator or admin
        $moderatedGroups = Group::whereHas('members', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->whereIn('group_members.role', ['admin', 'moderator']);
        })->pluck('id');

        // Get all pending join requests for these groups
        $joinRequests = \App\Models\GroupMember::with(['user', 'group'])
            ->whereIn('group_id', $moderatedGroups)
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('groups.join-requests', compact('joinRequests'));
    }

    /**
     * Approve a pending member
     */
    public function approveMember(Group $group, User $user)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to approve members.');
        }

        $group->members()->updateExistingPivot($user->id, [
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // Notify the approved user with the new approval notification
        $user->notify(new \App\Notifications\GroupMembershipApproved($user, $group, auth()->user()));

        // Notify group creator about new member (if different from approver)
        if ($group->creator && $group->creator->id !== auth()->id() && $group->creator->id !== $user->id) {
            $group->creator->notify(new \App\Notifications\GroupMemberJoined($user, $group));
        }

        return back()->with('success', 'Member approved successfully!');
    }

    /**
     * Reject a pending member
     */
    public function rejectMember(Group $group, User $user)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to reject members.');
        }

        // Notify the user about rejection before removing them
        $user->notify(new \App\Notifications\GroupMembershipRejected($user, $group, auth()->user()));

        $group->members()->detach($user->id);

        return back()->with('success', 'Member request rejected.');
    }

    /**
     * Remove a member from the group
     */
    public function removeMember(Group $group, User $user)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to remove members.');
        }

        // Prevent removing the group creator
        if ($group->created_by === $user->id) {
            return back()->with('error', 'Cannot remove the group creator.');
        }

        $group->members()->detach($user->id);

        return back()->with('success', 'Member removed successfully.');
    }

    /**
     * Update member role
     */
    public function updateMemberRole(Request $request, Group $group, User $user)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to update member roles.');
        }

        $validated = $request->validate([
            'role' => 'required|in:member,moderator,admin'
        ]);

        // Prevent changing creator's role
        if ($group->created_by === $user->id) {
            return back()->with('error', 'Cannot change the group creator\'s role.');
        }

        $group->members()->updateExistingPivot($user->id, [
            'role' => $validated['role']
        ]);

        return back()->with('success', 'Member role updated successfully.');
    }

    /**
     * Show pending posts for approval
     */
    public function pendingPosts(Group $group)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to moderate posts.');
        }

        $pendingPosts = $group->pendingPosts()
            ->with(['user', 'group', 'likes', 'comments', 'fileAttachments', 'tags', 'postMethod'])
            ->latest('created_at')
            ->paginate(10);

        return view('groups.pending-posts', compact('group', 'pendingPosts'));
    }

    /**
     * Approve a pending post
     */
    public function approvePost(Group $group, Post $post)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to approve posts.');
        }

        $post->update([
            'approval_status' => 'approved',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        // Notify the post author
        if ($post->user && $post->user->id !== auth()->id()) {
            $post->user->notify(new \App\Notifications\GroupPostApproved($post, $group, auth()->user()));
        }

        return back()->with('success', 'Post approved successfully!');
    }

    /**
     * Reject a pending post
     */
    public function rejectPost(Group $group, Post $post)
    {
        // Check permissions
        if (!$group->userCanModerate(auth()->user())) {
            abort(403, 'You do not have permission to reject posts.');
        }

        $post->update([
            'approval_status' => 'rejected',
            'approved_by' => auth()->id(),
        ]);

        // Notify the post author
        if ($post->user && $post->user->id !== auth()->id()) {
            $post->user->notify(new \App\Notifications\GroupPostRejected($post, $group, auth()->user()));
        }

        return back()->with('success', 'Post rejected.');
    }
}
