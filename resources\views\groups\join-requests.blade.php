<x-layouts.unilink-layout>
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Join Requests</h1>
                    <p class="text-gray-600 mt-1">Manage pending membership requests for your groups</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('groups.my') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        My Groups
                    </a>
                    <a href="{{ route('groups.index') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                        Browse Groups
                    </a>
                </div>
            </div>
        </div>

        <!-- Join Requests -->
        @if($joinRequests->count() > 0)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        Pending Requests ({{ $joinRequests->total() }})
                    </h2>
                </div>
                
                <div class="divide-y divide-gray-200">
                    @foreach($joinRequests as $request)
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <!-- User Avatar -->
                                    <img class="h-12 w-12 rounded-full"
                                         src="{{ $request->user->avatar ? Storage::disk('public')->url($request->user->avatar) : '/default-avatar.svg' }}"
                                         alt="{{ $request->user->name }}"
                                         onerror="this.src='/default-avatar.svg'; this.onerror=null;">
                                    
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3">
                                            <h3 class="text-lg font-medium text-gray-900">{{ $request->user->name }}</h3>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Pending
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-500">{{ $request->user->email }}</p>
                                        <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                            <span>Requested to join: 
                                                <a href="{{ route('groups.show', $request->group) }}" class="text-blue-600 hover:text-blue-800 font-medium">
                                                    {{ $request->group->name }}
                                                </a>
                                            </span>
                                            <span>{{ $request->created_at->diffForHumans() }}</span>
                                        </div>
                                        
                                        <!-- Group Info -->
                                        <div class="flex items-center space-x-2 mt-2">
                                            @if($request->group->logo)
                                                <img src="{{ Storage::disk('public')->url($request->group->logo) }}" 
                                                     alt="{{ $request->group->name }}" 
                                                     class="w-6 h-6 rounded object-cover">
                                            @else
                                                <div class="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                                                    <span class="text-blue-600 font-bold text-xs">{{ substr($request->group->name, 0, 1) }}</span>
                                                </div>
                                            @endif
                                            <span class="text-sm text-gray-600">{{ $request->group->name }}</span>
                                            @if($request->group->visibility === 'private')
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Private
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex items-center space-x-3">
                                    <form action="{{ route('groups.approve-member', [$request->group, $request->user]) }}" method="POST" class="inline">
                                        @csrf
                                        <button type="submit" 
                                                class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                            Approve
                                        </button>
                                    </form>
                                    
                                    <form action="{{ route('groups.reject-member', [$request->group, $request->user]) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                onclick="return confirm('Are you sure you want to reject this membership request?')"
                                                class="bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                            Reject
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                @if($joinRequests->hasPages())
                    <div class="px-6 py-4 border-t border-gray-200">
                        {{ $joinRequests->links() }}
                    </div>
                @endif
            </div>
        @else
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No pending requests</h3>
                <p class="mt-1 text-sm text-gray-500">There are no pending membership requests for your groups.</p>
                <div class="mt-6">
                    <a href="{{ route('groups.my') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        View My Groups
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-layouts.unilink-layout>
