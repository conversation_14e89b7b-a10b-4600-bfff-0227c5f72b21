<x-layouts.unilink-layout>
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">My Followers</h1>
                    <p class="text-gray-600 mt-1">{{ number_format($followers->total()) }} people follow you</p>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('follow-management.following') }}" 
                       class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                        View Following
                    </a>
                    <a href="{{ route('profile.show') }}" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative" id="followers-search-container">
                        <input type="text"
                               id="followers-search-input"
                               placeholder="Search followers by name or email..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                               autocomplete="off">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>

                        <!-- Search Results Dropdown -->
                        <div id="followers-search-results"
                             class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 max-h-96 overflow-y-auto z-50 hidden">
                            <div id="followers-search-loading" class="p-4 text-center text-gray-500 hidden">
                                <div class="inline-flex items-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Searching...
                                </div>
                            </div>
                            <div id="followers-search-content"></div>
                        </div>
                    </div>
                </div>
                @if($search)
                    <a href="{{ route('follow-management.followers') }}"
                       class="text-gray-500 hover:text-gray-700 px-3 py-2 transition-colors">
                        Clear
                    </a>
                @endif
            </div>
        </div>

        <!-- Followers List -->
        <div class="bg-white rounded-lg shadow-sm">
            @if($followers->count() > 0)
                <div class="divide-y divide-gray-200">
                    @foreach($followers as $follower)
                        <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                            <div class="flex items-center space-x-4">
                                <img src="{{ $follower->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($follower->avatar) : '/default-avatar.svg' }}"
                                     alt="{{ $follower->name }}"
                                     class="w-16 h-16 rounded-full object-cover"
                                     onerror="this.src='/default-avatar.svg'; this.onerror=null;">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <a href="{{ route('profile.user', $follower) }}"
                                           class="hover:text-blue-600 transition-colors">
                                            {{ $follower->name }}
                                        </a>
                                    </h3>
                                    @if($follower->hasCompleteProfile())
                                        <p class="text-gray-600">{{ $follower->email }}</p>
                                        @if($follower->bio)
                                            <p class="text-sm text-gray-500 mt-1">{{ Str::limit($follower->bio, 40) }}</p>
                                        @endif
                                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                            <span>{{ $follower->followers()->count() }} followers</span>
                                            <span>{{ $follower->following()->count() }} following</span>
                                            <span>Followed you {{ $follower->pivot->created_at->diffForHumans() }}</span>
                                        </div>
                                    @else
                                        <p class="text-sm text-gray-500">Basic profile</p>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                @if(auth()->id() !== $follower->id)
                                    <livewire:user-follower :user="$follower" :key="'follower-' . $follower->id" />
                                @endif
                                <a href="{{ route('profile.user', $follower) }}" 
                                   class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                                    View
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $followers->links() }}
                </div>
            @else
                <div class="p-12 text-center">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        @if($search)
                            No followers found
                        @else
                            No followers yet
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if($search)
                            Try adjusting your search terms.
                        @else
                            When people follow you, they'll appear here.
                        @endif
                    </p>
                    @if($search)
                        <div class="mt-4">
                            <a href="{{ route('follow-management.followers') }}" 
                               class="text-blue-600 hover:text-blue-800 font-medium">
                                View all followers
                            </a>
                        </div>
                    @endif
                </div>
            @endif
        </div>
    </div>
</x-layouts.unilink-layout>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('followers-search-input');
    const searchResults = document.getElementById('followers-search-results');
    const searchContent = document.getElementById('followers-search-content');
    const searchLoading = document.getElementById('followers-search-loading');
    let searchTimeout;

    function showSearchResults() {
        searchResults.classList.remove('hidden');
    }

    function hideSearchResults() {
        searchResults.classList.add('hidden');
    }

    function showLoading() {
        searchLoading.classList.remove('hidden');
        searchContent.innerHTML = '';
    }

    function hideLoading() {
        searchLoading.classList.add('hidden');
    }

    function performSearch(query) {
        fetch(`{{ route('follow-management.followers.search') }}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success && data.followers.length > 0) {
                    let html = '';
                    data.followers.forEach(follower => {
                        html += `
                            <div class="p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                <div class="flex items-center space-x-3">
                                    <img src="${follower.avatar_url}"
                                         alt="${follower.name}"
                                         class="w-10 h-10 rounded-full object-cover">
                                    <div class="flex-1">
                                        <div class="font-medium text-gray-900">
                                            <a href="${follower.profile_url}" class="hover:text-blue-600">
                                                ${follower.name}
                                            </a>
                                        </div>
                                        ${follower.has_complete_profile ? `
                                            <div class="text-sm text-gray-600">${follower.email}</div>
                                            ${follower.bio ? `<div class="text-sm text-gray-500 mt-1">${follower.bio.substring(0, 40)}${follower.bio.length > 40 ? '...' : ''}</div>` : ''}
                                        ` : `
                                            <div class="text-sm text-gray-500">Basic profile</div>
                                        `}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    searchContent.innerHTML = html;
                } else {
                    searchContent.innerHTML = `
                        <div class="p-4 text-center text-gray-500">
                            <div class="text-sm">No followers found matching "${query}"</div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Search error:', error);
                searchContent.innerHTML = `
                    <div class="p-4 text-center text-red-500">
                        <div class="text-sm">Error searching followers. Please try again.</div>
                    </div>
                `;
            });
    }

    if (searchInput) {
        // Handle search input
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length === 0) {
                hideSearchResults();
                return;
            }

            // Show loading state
            showSearchResults();
            showLoading();

            // Dynamic search with minimal delay
            const delay = query.length === 1 ? 300 : 150;
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, delay);
        });

        // Handle focus and blur events
        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length > 0) {
                showSearchResults();
            }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                hideSearchResults();
            }
        });

        // Handle escape key
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideSearchResults();
                this.blur();
            }
        });
    }
});
</script>
