<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Campus extends Model
{
    use HasFactory;

    protected $fillable = [
        'school_id',
        'name',
        'slug',
        'description',
        'address',
        'contact_info',
        'is_main_campus',
        'status',
    ];

    protected $casts = [
        'contact_info' => 'array',
        'is_main_campus' => 'boolean',
    ];

    /**
     * Get the school this campus belongs to
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get all users from this campus
     */
    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all organizations from this campus
     */
    public function organizations(): HasMany
    {
        return $this->hasMany(Organization::class);
    }

    /**
     * Get all groups from this campus
     */
    public function groups(): HasMany
    {
        return $this->hasMany(Group::class);
    }

    /**
     * Scope to get only active campuses
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get main campus only
     */
    public function scopeMainCampus($query)
    {
        return $query->where('is_main_campus', true);
    }

    /**
     * Get campus statistics
     */
    public function getStatistics()
    {
        return [
            'total_students' => $this->users()->count(),
            'total_organizations' => $this->organizations()->where('status', 'active')->count(),
            'total_groups' => $this->groups()->where('status', 'active')->count(),
            'recent_posts' => $this->getRecentPostsCount(),
        ];
    }

    /**
     * Get count of recent posts from campus users
     */
    public function getRecentPostsCount()
    {
        return \App\Models\Post::whereIn('user_id', $this->users()->pluck('id'))
            ->where('status', 'published')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();
    }

    /**
     * Get recent organizations from this campus
     */
    public function getRecentOrganizations($limit = 6)
    {
        return $this->organizations()
            ->where('status', 'active')
            ->withCount('activeMembers')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent groups from this campus
     */
    public function getRecentGroups($limit = 6)
    {
        return $this->groups()
            ->where('status', 'active')
            ->withCount('activeMembers')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent students from this campus
     */
    public function getRecentStudents($limit = 12)
    {
        return $this->users()
            ->where('role', 'student')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent posts from campus users
     */
    public function getRecentPosts($limit = 10)
    {
        return \App\Models\Post::whereIn('user_id', $this->users()->pluck('id'))
            ->where('status', 'published')
            ->with(['user', 'tags', 'reactions'])
            ->withCount(['comments', 'reactions'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
