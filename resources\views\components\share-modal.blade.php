@props(['post'])

<!-- Share Modal -->
<div id="shareModal-{{ $post->id }}" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Share Post</h3>
                <button onclick="closeShareModal({{ $post->id }})" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Post Preview -->
            <div class="mb-4 p-3 bg-gray-50 rounded-lg border">
                <div class="flex items-center space-x-2 mb-2">
                    @if($post->organization)
                        <img class="h-8 w-8 rounded-full"
                             src="{{ $post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->organization->logo) : '/default-avatar.svg' }}"
                             alt="{{ $post->organization->name }}"
                             onerror="this.src='/default-avatar.svg'; this.onerror=null;">
                        <span class="font-medium text-sm text-gray-900">{{ $post->organization->name }}</span>
                    @else
                        <img class="h-8 w-8 rounded-full" 
                             src="{{ $post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : '/default-avatar.svg' }}"
                             alt="{{ $post->user->name }}"
                             onerror="this.src='/default-avatar.svg'; this.onerror=null;">
                        <span class="font-medium text-sm text-gray-900">{{ $post->user->name }}</span>
                    @endif
                </div>
                <h4 class="font-medium text-gray-900 text-sm mb-1">{{ $post->title }}</h4>
                <p class="text-gray-600 text-xs">{{ Str::limit($post->content, 100) }}</p>
            </div>

            <!-- Share Options -->
            <div class="space-y-3">
                <!-- Social Media Sharing -->
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="shareToFacebook({{ $post->id }})" 
                            class="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        <span class="text-sm">Facebook</span>
                    </button>
                    
                    <button onclick="shareToTwitter({{ $post->id }})" 
                            class="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        <span class="text-sm">Twitter</span>
                    </button>
                    
                    <button onclick="shareToLinkedIn({{ $post->id }})" 
                            class="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-5 h-5 text-blue-700" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        <span class="text-sm">LinkedIn</span>
                    </button>
                    
                    <button onclick="copyPostLink({{ $post->id }})" 
                            class="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <span class="text-sm">Copy Link</span>
                    </button>
                </div>

                <!-- Internal Share (within platform) -->
                @auth
                    <div class="border-t pt-3">
                        <form class="internal-share-form" data-post-id="{{ $post->id }}">
                            @csrf

                            <!-- Privacy Scope Selector -->
                            <div class="mb-3">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Who can see this?</label>
                                <div class="relative" x-data="{ open: false, selected: 'public' }">
                                    <button type="button" @click="open = !open"
                                            class="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                                        <div class="flex items-center space-x-2">
                                            <svg x-show="selected === 'public'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <svg x-show="selected === 'friends'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                            </svg>
                                            <svg x-show="selected === 'only_me'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                            </svg>
                                            <svg x-show="selected === 'custom'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            <span x-text="selected === 'public' ? 'Public' : selected === 'friends' ? 'Friends' : selected === 'only_me' ? 'Only me' : 'Custom'"></span>
                                        </div>
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>

                                    <div x-show="open" @click.away="open = false" x-transition
                                         class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">

                                        <div @click="selected = 'public'; open = false"
                                             class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50">
                                            <div class="flex items-center space-x-3">
                                                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <div>
                                                    <div class="font-medium text-gray-900">Public</div>
                                                    <div class="text-sm text-gray-500">Anyone can see this</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div @click="selected = 'friends'; open = false"
                                             class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50">
                                            <div class="flex items-center space-x-3">
                                                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                                </svg>
                                                <div>
                                                    <div class="font-medium text-gray-900">Friends</div>
                                                    <div class="text-sm text-gray-500">Your friends can see this</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div @click="selected = 'only_me'; open = false"
                                             class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50">
                                            <div class="flex items-center space-x-3">
                                                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                </svg>
                                                <div>
                                                    <div class="font-medium text-gray-900">Only me</div>
                                                    <div class="text-sm text-gray-500">Only you can see this</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div @click="selected = 'custom'; open = false"
                                             class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50">
                                            <div class="flex items-center space-x-3">
                                                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                                <div>
                                                    <div class="font-medium text-gray-900">Custom</div>
                                                    <div class="text-sm text-gray-500">Custom privacy settings</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Hidden input to store selected value -->
                                    <input type="hidden" name="privacy_scope" :value="selected">
                                </div>
                            </div>

                            <textarea name="message" rows="2"
                                      placeholder="Add a message (optional)..."
                                      class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none mb-3"></textarea>
                            <button type="submit" class="w-full bg-custom-green text-white py-2 px-4 rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                                Share to Your Timeline
                            </button>
                        </form>
                    </div>
                @endauth
            </div>
        </div>
    </div>
</div>
